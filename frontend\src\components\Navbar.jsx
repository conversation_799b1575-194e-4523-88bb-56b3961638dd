import React, { useState, useEffect } from 'react'
import logo from "../images/logos/logo.png"
import { Link, useLocation } from 'react-router-dom'
import { FiHome, FiLogOut, FiUser } from 'react-icons/fi'

const Navbar = () => {
  const location = useLocation();
  const [userName, setUserName] = useState('');

  // Get user name from localStorage when component mounts
  useEffect(() => {
    // Try to get user name from localStorage
    const storedUserName = localStorage.getItem('userName');

    if (storedUserName) {
      setUserName(storedUserName);
    } else {
      // If not in localStorage, try to get from fullName in localStorage
      const fullName = localStorage.getItem('fullName');
      if (fullName) {
        setUserName(fullName);
      }
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("isLoggedIn");
    window.location.reload();
  };

  return (
    <nav
      className="sticky top-0 z-50 backdrop-blur-xl bg-transparent overflow-hidden"
      style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="flex items-center justify-between h-[70px] relative">
          {/* Logo */}
          <div className="flex items-center">
            <div>
              <Link
                to="/"
                className="flex items-center relative"
              >
                <img
                  src={logo}
                  className='w-[170px] object-cover'
                  alt="KodeBase Logo"
                />
              </Link>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="flex items-center space-x-6">
              <div>
                <Link
                  to="/"
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${location.pathname === '/' ? 'text-primary-400 bg-primary-900/20' : 'text-gray-300 hover:text-primary-400 hover:bg-dark-100'}`}
                >
                  <FiHome className="h-4 w-4" />
                  <span>Home</span>
                </Link>
              </div>

              <div>
                <Link
                  to="/guide"
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${location.pathname.includes('/guide') ? 'text-primary-400 bg-primary-900/20' : 'text-gray-300 hover:text-primary-400 hover:bg-dark-100'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                  </svg>
                  <span>Guide</span>
                </Link>
              </div>
            </div>
          </div>

          {/* User Name Display */}
          {userName && (
            <div className="hidden md:flex items-center mr-4 px-3 py-2 rounded-md text-sm font-medium text-primary-300 bg-gradient-to-r from-primary-900/30 to-accent-900/30 backdrop-blur-sm border border-primary-500/20">
              <FiUser className="h-4 w-4 mr-2 text-primary-400" />
              <span>{userName}</span>
            </div>
          )}

          {/* Logout Button */}
          <div>
            <button
              onClick={handleLogout}
              className="flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium text-white bg-gradient-to-r from-secondary-600 to-secondary-700 hover:from-secondary-700 hover:to-secondary-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-500 shadow-lg"
            >
              <FiLogOut className="h-4 w-4" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu - shown on small screens */}
      <div className="md:hidden relative">
        {/* User name display in mobile menu */}
        {userName && (
          <div className="px-4 pt-2 text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium text-primary-300 bg-gradient-to-r from-primary-900/30 to-accent-900/30 backdrop-blur-sm border border-primary-500/20">
              <FiUser className="h-3 w-3 mr-1 text-primary-400" />
              <span>{userName}</span>
            </div>
          </div>
        )}

        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex justify-around">
          <div>
            <Link
              to="/"
              className={`flex flex-col items-center px-3 py-2 rounded-md text-xs font-medium transition-all duration-200 ${location.pathname === '/' ? 'text-primary-400' : 'text-gray-300 hover:text-primary-400'}`}
            >
              <FiHome className="h-5 w-5 mb-1" />
              <span>Home</span>
            </Link>
          </div>

          <div>
            <Link
              to="/guide"
              className={`flex flex-col items-center px-3 py-2 rounded-md text-xs font-medium transition-all duration-200 ${location.pathname.includes('/guide') ? 'text-primary-400' : 'text-gray-300 hover:text-primary-400'}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mb-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
              </svg>
              <span>Guide</span>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
