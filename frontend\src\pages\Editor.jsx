import { useEffect, useState } from 'react';
import Navbar from '../components/Navbar';
import MonacoEditor from '@monaco-editor/react';
import { useParams, useNavigate } from 'react-router-dom';
import { api_base_url } from '../helper';
import { toast } from 'react-toastify';
import { FiPlay, FiSave, FiArrowLeft, FiCpu, FiClock, FiCode } from 'react-icons/fi';

const Editor = () => {
  const navigate = useNavigate();
  const [code, setCode] = useState(""); // State to hold the code
  const { id } = useParams(); // Extract project ID from URL params
  const [output, setOutput] = useState("");
  const [error, setError] = useState(false);

  const [data, setData] = useState(null);

  // Fetch project data on mount
  useEffect(() => {
    fetch(`${api_base_url}/getProject`, {
      mode: 'cors',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: localStorage.getItem('token'),
        projectId: id,
      }),
    })
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          setCode(data.project.code); // Set the fetched code
          setData(data.project);
        } else {
          toast.error(data.msg);
        }
      })
      .catch((err) => {
        console.error('Error fetching project:', err);
        toast.error('Failed to load project.');
      });
  }, [id]);

  // Save project function
  const saveProject = () => {
    const trimmedCode = code?.toString().trim(); // Ensure code is a string and trimmed
    console.log('Saving code:', trimmedCode); // Debug log

    fetch(`${api_base_url}/saveProject`, {
      mode: 'cors',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: localStorage.getItem('token'),
        projectId: id,
        code: trimmedCode, // Use the latest code state
      }),
    })
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          toast.success(data.msg);
        } else {
          toast.error(data.msg);
        }
      })
      .catch((err) => {
        console.error('Error saving project:', err);
        toast.error('Failed to save the project.');
      });
  };

  // Shortcut handler for saving with Ctrl+S
  const handleSaveShortcut = (e) => {
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault(); // Prevent browser's default save behavior
      saveProject(); // Call the save function
    }
  };

  // Add and clean up keyboard event listener
  useEffect(() => {
    window.addEventListener('keydown', handleSaveShortcut);
    return () => {
      window.removeEventListener('keydown', handleSaveShortcut);
    };
  }, [code]); // Reattach when `code` changes

  const runProject = () => {
    fetch("https://emkc.org/api/v2/piston/execute", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        language: data.projLanguage,
        version: data.version,
        files: [
          {
            filename: data.name + data.projLanguage === "python" ? ".py" : data.projLanguage === "java" ? ".java" : data.projLanguage === "javascript" ? ".js" : data.projLanguage === "c" ? ".c" : data.projLanguage === "cpp" ? ".cpp" : data.projLanguage === "bash" ? ".sh" : "",
            content: code
          }
        ]
      })
    }).then(res => res.json()).then(data => {
      console.log(data)
      setOutput(data.run.output);
      setError(data.run.code === 1 ? true : false);
    })
  }

  // Determine language for editor
  const getEditorLanguage = () => {
    if (!data) return 'plaintext';

    const langMap = {
      python: 'python',
      javascript: 'javascript',
      cpp: 'cpp',
      c: 'c',
      java: 'java',
      bash: 'shell'
    };

    return langMap[data.projLanguage] || 'plaintext';
  };

  // Loading state
  const [isRunning, setIsRunning] = useState(false);

  // Enhanced run function with loading state
  const handleRunProject = () => {
    setIsRunning(true);
    setOutput('Running code...');
    setError(false);

    runProject();

    // Set a minimum loading time for better UX
    setTimeout(() => {
      setIsRunning(false);
    }, 500);
  };

  return (
    <>
      <Navbar />
      <div className="max-w-full mx-auto">
        {/* Project info and actions bar */}
        <div className="border-b shadow-sm bg-dark-200 border-dark-100">
          <div className="px-4 py-3 mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
              {/* Project info */}
              <div className="flex items-center gap-3">
                <button
                  onClick={() => navigate('/')}
                  className="p-2 text-gray-400 transition-colors duration-200 rounded-lg hover:text-white hover:bg-dark-100"
                  title="Back to Projects"
                >
                  <FiArrowLeft className="w-5 h-5" />
                </button>

                {data && (
                  <div>
                    <h1 className="text-lg font-medium text-white">{data.name}</h1>
                    <div className="flex items-center gap-2 text-xs text-gray-400">
                      <FiCode className="h-3.5 w-3.5" />
                      <span>{data.projLanguage.toUpperCase()}</span>
                      <span className="mx-1">•</span>
                      <FiCpu className="h-3.5 w-3.5" />
                      <span>{data.version}</span>
                      <span className="mx-1">•</span>
                      <FiClock className="h-3.5 w-3.5" />
                      <span>Last edited: {new Date(data.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Action buttons */}
              <div className="flex items-center gap-2">
                <button
                  onClick={saveProject}
                  className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white transition-colors duration-200 bg-gray-700 rounded-lg hover:bg-gray-600"
                  title="Save (Ctrl+S)"
                >
                  <FiSave className="w-4 h-4" />
                  Save
                </button>

                <button
                  onClick={handleRunProject}
                  disabled={isRunning}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors duration-200 text-sm font-medium ${isRunning ? 'bg-primary-700 text-primary-300 cursor-not-allowed' : 'bg-primary-600 hover:bg-primary-700 text-white'}`}
                  title="Run Code"
                >
                  {isRunning ? (
                    <>
                      <svg className="w-4 h-4 animate-spin text-primary-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Running...
                    </>
                  ) : (
                    <>
                      <FiPlay className="w-4 h-4" />
                      Run
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Editor and output panels */}
        <div className="editor-layout flex h-[calc(100vh-140px)]">
          {/* Code editor panel */}
          <div className="w-1/2 h-full border-r left border-dark-100">
            <div className="h-full editor-container">
              <MonacoEditor
                onChange={(newCode) => setCode(newCode || '')}
                theme="vs-dark"
                height="100%"
                width="100%"
                language={getEditorLanguage()}
                value={code}
                options={{
                  minimap: { enabled: true },
                  scrollBeyondLastLine: false,
                  fontSize: 14,
                  fontFamily: '"Fira Code", Menlo, Monaco, "Courier New", monospace',
                  lineNumbers: 'on',
                  automaticLayout: true,
                  tabSize: 2,
                  wordWrap: 'on',
                  renderLineHighlight: 'all',
                  scrollbar: {
                    vertical: 'auto',
                    horizontal: 'auto',
                    verticalScrollbarSize: 10,
                    horizontalScrollbarSize: 10,
                  }
                }}
              />
            </div>
          </div>

          {/* Output panel */}
          <div className="w-1/2 h-full right bg-dark-200 output-panel">
            <div className="flex items-center justify-between px-4 py-3 border-b border-dark-100">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-300">Output</span>
                {error && (
                  <span className="px-2 py-0.5 text-xs font-medium bg-red-900/20 text-red-500 rounded-full">
                    Error
                  </span>
                )}
              </div>

              {output && (
                <button
                  onClick={() => setOutput('')}
                  className="text-xs text-gray-400 transition-colors duration-200 hover:text-white"
                >
                  Clear
                </button>
              )}
            </div>

            <div className="p-4 overflow-auto h-[calc(100%-48px)]">
              {!output && !isRunning ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 mb-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-400">Run your code to see the output here</p>
                </div>
              ) : (
                <pre className={`font-mono text-sm whitespace-pre-wrap ${error ? "text-red-400" : "text-gray-300"}`}>
                  {output}
                </pre>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Editor;
