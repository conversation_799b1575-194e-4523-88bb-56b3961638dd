import React, { useState } from 'react';
import Navbar from '../components/Navbar';
import bgImage from "../images/bgimg/hassan-maayiz-PclsTXKOgHU-unsplash.jpg";

const Guide = () => {
  const [showContactModal, setShowContactModal] = useState(false);
  return (
    <div
      style={{
        background: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed',
        minHeight: '100vh'
      }}
    >
      <Navbar />
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 text-transparent bg-clip-text mb-4">
            KodeBase User Guide
          </h1>
          <p className="text-primary-300/80 text-lg">
            Learn how to use our online code editor platform effectively
          </p>
        </div>

        <div className="space-y-12">
          {/* Getting Started Section */}
          <section className="glass-effect rounded-2xl p-8">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <span className="bg-primary-600 text-white w-8 h-8 rounded-full flex items-center justify-center mr-3">1</span>
              Getting Started
            </h2>
            <div className="space-y-4">
              <div className="glass-button rounded-xl p-6">
                <h3 className="text-lg font-medium text-white mb-2">Creating Your First Project</h3>
                <p className="text-gray-300 mb-4">
                  To create a new coding project, follow these simple steps:
                </p>
                <ol className="list-decimal list-inside space-y-2 text-gray-300">
                  <li>Click the <span className="text-primary-400 font-medium">"Create New Project"</span> button on the home page</li>
                  <li>Enter a name for your project</li>
                  <li>Select a programming language from the dropdown menu</li>
                  <li>Click <span className="text-primary-400 font-medium">"Create Project"</span> to start coding</li>
                </ol>
              </div>
            </div>
          </section>

          {/* Code Editor Section */}
          <section className="glass-effect rounded-2xl p-8">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <span className="bg-primary-600 text-white w-8 h-8 rounded-full flex items-center justify-center mr-3">2</span>
              Using the Code Editor
            </h2>
            <div className="space-y-4">
              <div className="glass-button rounded-xl p-6">
                <h3 className="text-lg font-medium text-white mb-2">Writing and Running Code</h3>
                <p className="text-gray-300 mb-4">
                  Our code editor provides a professional environment for writing and executing code:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Write your code in the left panel</li>
                  <li>Click the <span className="text-primary-400 font-medium">"Run"</span> button to execute your code</li>
                  <li>View the output in the right panel</li>
                  <li>Save your changes by clicking the <span className="text-primary-400 font-medium">"Save"</span> button</li>
                </ul>
              </div>

              <div className="glass-button rounded-xl p-6">
                <h3 className="text-lg font-medium text-white mb-2">Keyboard Shortcuts</h3>
                <p className="text-gray-300 mb-4">
                  Use these keyboard shortcuts to improve your productivity:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Save</span>
                    <span className="glass-button px-2 py-1 rounded text-sm text-primary-300">Ctrl + S</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Find</span>
                    <span className="glass-button px-2 py-1 rounded text-sm text-primary-300">Ctrl + F</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Replace</span>
                    <span className="glass-button px-2 py-1 rounded text-sm text-primary-300">Ctrl + H</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Comment Line</span>
                    <span className="glass-button px-2 py-1 rounded text-sm text-primary-300">Ctrl + /</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Managing Projects Section */}
          <section className="glass-effect rounded-2xl p-8">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <span className="bg-primary-600 text-white w-8 h-8 rounded-full flex items-center justify-center mr-3">3</span>
              Managing Your Projects
            </h2>
            <div className="space-y-4">
              <div className="glass-button rounded-xl p-6">
                <h3 className="text-lg font-medium text-white mb-2">Project Management</h3>
                <p className="text-gray-300 mb-4">
                  You can manage your projects from the home page:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>View all your projects in the grid layout</li>
                  <li>Click <span className="text-primary-400 font-medium">"Open Editor"</span> to continue working on a project</li>
                  <li>Use the edit button to rename a project</li>
                  <li>Use the delete button to remove a project (this action cannot be undone)</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Supported Languages Section */}
          <section className="glass-effect rounded-2xl p-8">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <span className="bg-primary-600 text-white w-8 h-8 rounded-full flex items-center justify-center mr-3">4</span>
              Supported Languages
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <div className="glass-button rounded-xl p-4 flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-2 mr-3">
                  <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" alt="Python" className="w-full h-full object-contain" />
                </div>
                <div>
                  <h3 className="text-white font-medium">Python</h3>
                  <p className="text-xs text-gray-400">General-purpose language</p>
                </div>
              </div>

              <div className="glass-button rounded-xl p-4 flex items-center">
                <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center p-2 mr-3">
                  <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg" alt="JavaScript" className="w-full h-full object-contain" />
                </div>
                <div>
                  <h3 className="text-white font-medium">JavaScript</h3>
                  <p className="text-xs text-gray-400">Web development</p>
                </div>
              </div>

              <div className="glass-button rounded-xl p-4 flex items-center">
                <div className="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center p-2 mr-3">
                  <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg" alt="C++" className="w-full h-full object-contain" />
                </div>
                <div>
                  <h3 className="text-white font-medium">C++</h3>
                  <p className="text-xs text-gray-400">Systems programming</p>
                </div>
              </div>

              <div className="glass-button rounded-xl p-4 flex items-center">
                <div className="w-10 h-10 bg-blue-800 rounded-lg flex items-center justify-center p-2 mr-3">
                  <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/c/c-original.svg" alt="C" className="w-full h-full object-contain" />
                </div>
                <div>
                  <h3 className="text-white font-medium">C</h3>
                  <p className="text-xs text-gray-400">Low-level programming</p>
                </div>
              </div>

              <div className="glass-button rounded-xl p-4 flex items-center">
                <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center p-2 mr-3">
                  <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg" alt="Java" className="w-full h-full object-contain" />
                </div>
                <div>
                  <h3 className="text-white font-medium">Java</h3>
                  <p className="text-xs text-gray-400">Enterprise applications</p>
                </div>
              </div>

              <div className="glass-button rounded-xl p-4 flex items-center">
                <div className="w-10 h-10 bg-green-700 rounded-lg flex items-center justify-center p-2 mr-3">
                  <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bash/bash-original.svg" alt="Bash" className="w-full h-full object-contain" />
                </div>
                <div>
                  <h3 className="text-white font-medium">Bash</h3>
                  <p className="text-xs text-gray-400">Shell scripting</p>
                </div>
              </div>
            </div>
          </section>

          {/* Help & Support Section */}
          <section className="glass-effect rounded-2xl p-8">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <span className="bg-primary-600 text-white w-8 h-8 rounded-full flex items-center justify-center mr-3">5</span>
              Help & Support
            </h2>
            <div className="glass-button rounded-xl p-6">
              <p className="text-gray-300 mb-4">
                If you need additional help or have questions about using KodeBase, please contact our support team:
              </p>
              <div className="flex items-center justify-center gap-4 mt-6">
                <button
                  onClick={() => setShowContactModal(true)}
                  className="px-6 py-3 glass-button bg-gradient-to-r from-primary-600/80 to-secondary-600/80 hover:from-primary-700/90 hover:to-secondary-700/90 text-white rounded-xl transition-all duration-200 flex items-center gap-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  Contact Support
                </button>

                <a
                  href="/dfd"
                  className="px-6 py-3 glass-button bg-gradient-to-r from-secondary-600/80 to-accent-600/80 hover:from-secondary-700/90 hover:to-accent-700/90 text-white rounded-xl transition-all duration-200 flex items-center gap-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clipRule="evenodd" />
                  </svg>
                  View System Diagrams
                </a>
              </div>
            </div>
          </section>
        </div>
      </div>

      {/* Contact Modal */}
      {showContactModal && (
        <div
          className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={() => setShowContactModal(false)}
        >
          <div
            className="glass-form rounded-2xl overflow-hidden w-full max-w-md transition-all duration-300 transform"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal header */}
            <div className="p-5 border-b border-white/10 flex items-center justify-between">
              <h3 className="text-xl font-semibold text-white">Contact Support</h3>
              <button
                onClick={() => setShowContactModal(false)}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

            {/* Modal body */}
            <div className="p-6">
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-primary-600/20 rounded-full flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary-400" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <h4 className="text-lg font-medium text-white mb-2">Email Us</h4>
                <p className="text-gray-400 mb-4">For any questions or support, please contact us at:</p>

                <div className="glass-button p-4 rounded-xl w-full text-center mb-6">
                  <a
                    href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=KodeBase Support Request"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-400 font-medium hover:text-primary-300 transition-colors duration-200 flex items-center justify-center gap-2"
                  >
                    <span><EMAIL></span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                      <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                    </svg>
                  </a>
                </div>

                <p className="text-sm text-gray-500">We'll get back to you as soon as possible.</p>
              </div>
            </div>

            {/* Modal footer */}
            <div className="p-5 border-t border-white/10 flex justify-end">
              <button
                onClick={() => setShowContactModal(false)}
                className="px-4 py-2 glass-button bg-gray-700/50 hover:bg-gray-600/60 text-white rounded-xl transition-all duration-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Guide;
