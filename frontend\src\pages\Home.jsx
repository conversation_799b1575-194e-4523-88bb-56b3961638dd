import React, { useEffect, useState, version } from 'react';
import Navbar from "../components/Navbar";
import Select from 'react-select';
import bgImage from "../images/bgimg/pexels-pixabay-70577.jpg";
import { api_base_url } from '../helper';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

const Home = () => {
  const [isCreateModelShow, setIsCreateModelShow] = useState(false);
  const [languageOptions, setLanguageOptions] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState(null); // State to store selected language

  const [isEditModelShow, setIsEditModelShow] = useState(false);

  const navigate = useNavigate();

  const [name, setName] = useState("");

  const customStyles = {
    control: (provided) => ({
      ...provided,
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      color: '#fff',
      padding: '5px',
      borderRadius: '8px',
      boxShadow: 'none',
      minHeight: '42px',
      '&:hover': {
        borderColor: 'rgba(14, 165, 233, 0.5)'
      }
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: '#171717',
      color: '#fff',
      width: "100%",
      borderRadius: '8px',
      overflow: 'hidden',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3)',
      zIndex: 9999,
      position: 'absolute'
    }),
    menuPortal: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
    menuList: (provided) => ({
      ...provided,
      padding: '5px',
      maxHeight: '200px'
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isFocused ? 'rgba(14, 165, 233, 0.2)' : 'transparent',
      color: state.isFocused ? '#38bdf8' : '#fff',
      cursor: 'pointer',
      borderRadius: '4px',
      margin: '2px 0',
      padding: '8px 12px',
      fontSize: '14px',
      '&:active': {
        backgroundColor: 'rgba(14, 165, 233, 0.3)'
      }
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#fff',
    }),
    placeholder: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.5)',
    }),
    input: (provided) => ({
      ...provided,
      color: '#fff',
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: '2px 8px',
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.5)',
      padding: '8px',
      '&:hover': {
        color: 'rgba(14, 165, 233, 0.8)'
      }
    }),
    indicatorSeparator: (provided) => ({
      ...provided,
      backgroundColor: 'rgba(255, 255, 255, 0.1)'
    }),
    clearIndicator: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.5)',
      padding: '8px',
      '&:hover': {
        color: 'rgba(255, 255, 255, 0.8)'
      }
    })
  };

  const getRunTimes = async () => {
    let res = await fetch("https://emkc.org/api/v2/piston/runtimes");
    let data = await res.json();

    // Filter only the required languages
    const filteredLanguages = [
      "python",
      "javascript",
      "c",
      "c++",
      "java",
      "bash"
    ];

    const options = data
      .filter(runtime => filteredLanguages.includes(runtime.language))
      .map(runtime => ({
        label: `${runtime.language} (${runtime.version})`,
        value: runtime.language === "c++" ? "cpp" : runtime.language,
        version: runtime.version,
      }));

    setLanguageOptions(options);
  };

  const handleLanguageChange = (selectedOption) => {
    setSelectedLanguage(selectedOption); // Update selected language state
    console.log("Selected language:", selectedOption);
  };

  const [projects, setProjects] = useState(null);

  const getProjects = async () => {
    fetch(api_base_url + "/getProjects", {
      mode: "cors",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        token: localStorage.getItem("token")
      })
    }).then(res => res.json()).then(data => {
      console.log(data)
      if (data.success) {
        setProjects(data.projects);
      }
      else {
        toast.error(data.msg);
      }
    });
  };

  useEffect(() => {
    getProjects();
    getRunTimes();
  }, []);

  const createProj = () => {
    fetch(api_base_url + "/createProj", {
      mode: "cors",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        name: name,
        projLanguage: selectedLanguage.value,
        token: localStorage.getItem("token"),
        version: selectedLanguage.version
      })
    }).then(res => res.json()).then(data => {
      if (data.success) {
        setName("");
        navigate("/editior/" + data.projectId)
      }
      else {
        toast.error(data.msg);
      }
    })
  };

  const deleteProject = (id) => {
    let conf = confirm("Are you sure you want to delete this project?");
    if (conf) {
      fetch(api_base_url + "/deleteProject", {
        mode: "cors",
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          projectId: id,
          token: localStorage.getItem("token")
        })
      }).then(res => res.json()).then(data => {
        if (data.success) {
          getProjects();
        }
        else {
          toast.error(data.msg);
        }
      });
    }
  };

  const [editProjId, setEditProjId] = useState("");

  const updateProj = () => {
    fetch(api_base_url + "/editProject", {
      mode: "cors",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        projectId: editProjId,
        token: localStorage.getItem("token"),
        name: name,
      })
    }).then(res => res.json()).then(data => {
      if (data.success) {
        setIsEditModelShow(false);
        setName("");
        setEditProjId("");
        getProjects();
      }
      else {
        toast.error(data.msg);
        setIsEditModelShow(false);
        setName("");
        setEditProjId("");
        getProjects();
      }
    })
  };

  return (
    <div
      style={{
        background: `
          linear-gradient(135deg, rgba(26, 26, 46, 0.85) 0%, rgba(22, 33, 62, 0.8) 50%, rgba(15, 52, 96, 0.75) 100%),
          url(${bgImage})
        `,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed',
        minHeight: '100vh'
      }}
    >
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header section */}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 text-transparent bg-clip-text">My Projects</h1>
            <p className="text-primary-300/80 mt-2">Create, manage and run your coding projects</p>
          </div>
          <button
            onClick={() => { setIsCreateModelShow(true) }}
            className="btnNormal !w-auto bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-medium py-2 px-4 rounded-lg shadow-lg hover:shadow-primary-500/40 transition-all duration-200 flex items-center gap-2 backdrop-blur-md border border-primary-500/30 hover:border-primary-400/60"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Create New Project
          </button>
        </div>

        {/* Projects grid */}
        <div className="projects">
          {!projects && (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
            </div>
          )}

          {projects && projects.length === 0 && (
            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-8 text-center border border-white/20 shadow-2xl"
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)',
                boxShadow: '0 25px 45px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-primary-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <h3 className="text-xl font-medium text-primary-300 mb-2">No projects found</h3>
              <p className="text-primary-400/70 mb-6">Get started by creating your first coding project</p>
              <button
                onClick={() => { setIsCreateModelShow(true) }}
                className="btnNormal !w-auto mx-auto bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-medium py-2 px-4 rounded-lg shadow-lg hover:shadow-primary-500/40 transition-all duration-200 flex items-center gap-2 backdrop-blur-md border border-primary-500/30 hover:border-primary-400/60"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                Create Project
              </button>
            </div>
          )}

          {projects && projects.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project, index) => {
                // Determine language icon and color
                const languageConfig = {
                  python: {
                    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg",
                    color: "bg-blue-600"
                  },
                  javascript: {
                    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg",
                    color: "bg-yellow-500"
                  },
                  cpp: {
                    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg",
                    color: "bg-blue-700"
                  },
                  c: {
                    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/c/c-original.svg",
                    color: "bg-blue-800"
                  },
                  java: {
                    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg",
                    color: "bg-red-600"
                  },
                  bash: {
                    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bash/bash-original.svg",
                    color: "bg-green-700"
                  }
                };

                const langConfig = languageConfig[project.projLanguage] || {
                  icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/devicon/devicon-original.svg",
                  color: "bg-gray-600"
                };

                return (
                  <div key={project._id} className="project bg-white/10 backdrop-blur-xl rounded-2xl overflow-hidden shadow-2xl border border-white/20 hover:border-primary-400/40 hover:shadow-primary-500/30 transition-all duration-300"
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                      boxShadow: '0 25px 45px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
                    }}
                  >
                    {/* Project header with language badge */}
                    <div className="p-5 border-b border-primary-500/20 bg-gradient-to-r from-primary-900/20 to-accent-900/20 backdrop-blur-md">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-lg ${langConfig.color} flex items-center justify-center p-2`}>
                            <img src={langConfig.icon} alt={project.projLanguage} className="w-full h-full object-contain" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-white truncate max-w-[180px]">{project.name}</h3>
                            <p className="text-xs text-gray-400">{project.projLanguage.toUpperCase()}</p>
                          </div>
                        </div>
                        <span className="text-xs text-gray-500">{new Date(project.date).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* Project actions */}
                    <div className="p-5 flex gap-3 bg-gradient-to-r from-dark-200/60 to-dark-300/50 backdrop-blur-md">
                      <button
                        onClick={() => { navigate("/editior/" + project._id) }}
                        className="flex-1 py-2 px-3 bg-primary-600/20 hover:bg-primary-600/40 text-primary-400 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm font-medium backdrop-blur-md border border-primary-500/10 hover:border-primary-500/30 shadow-sm hover:shadow-primary-500/20"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                        Open Editor
                      </button>
                      <button
                        onClick={() => {
                          setIsEditModelShow(true);
                          setEditProjId(project._id);
                          setName(project.name);
                        }}
                        className="p-2 bg-gray-700/50 hover:bg-gray-600/70 text-gray-300 rounded-lg transition-all duration-200 backdrop-blur-md border border-gray-500/10 hover:border-gray-500/30 shadow-sm hover:shadow-gray-500/20"
                        title="Edit Project"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                          <path fillRule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <button
                        onClick={() => { deleteProject(project._id) }}
                        className="p-2 bg-red-900/20 hover:bg-red-900/40 text-red-500 rounded-lg transition-all duration-200 backdrop-blur-md border border-red-500/10 hover:border-red-500/30 shadow-sm hover:shadow-red-500/20"
                        title="Delete Project"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Create Project Modal */}
      {isCreateModelShow && (
        <div
          onClick={(e) => {
            if (e.target.classList.contains("modal-overlay")) {
              setIsCreateModelShow(false);
              setName("");
              setSelectedLanguage(null);
            }
          }}
          className="modal-overlay fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        >
          <div
            className="bg-white/10 backdrop-blur-xl rounded-2xl overflow-hidden shadow-2xl border border-white/20 w-full max-w-md transition-all duration-300 transform"
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              boxShadow: '0 25px 45px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal header */}
            <div className="p-5 border-b border-primary-500/20 flex items-center justify-between bg-gradient-to-r from-primary-900/20 to-accent-900/20 backdrop-blur-md">
              <h3 className="text-xl font-semibold text-white">Create New Project</h3>
              <button
                onClick={() => {
                  setIsCreateModelShow(false);
                  setName("");
                  setSelectedLanguage(null);
                }}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

            {/* Modal body */}
            <div className="p-5">
              <div className="mb-4">
                <label htmlFor="project-name" className="block text-sm font-medium text-primary-300 mb-2">Project Name</label>
                <div className="inputBox">
                  <input
                    id="project-name"
                    onChange={(e) => { setName(e.target.value) }}
                    value={name}
                    type="text"
                    placeholder="Enter project name"
                    className="text-white w-full"
                    autoFocus
                  />
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="language-select" className="block text-sm font-medium text-primary-300 mb-2">Programming Language</label>
                <div className="relative z-50">
                  <Select
                    inputId="language-select"
                    placeholder="Select a language"
                    options={languageOptions}
                    styles={customStyles}
                    onChange={handleLanguageChange}
                    isSearchable
                    className="react-select-container"
                    classNamePrefix="react-select"
                    menuPortalTarget={document.body}
                    menuPosition="fixed"
                  />
                </div>
              </div>

              {selectedLanguage && (
                <div className="bg-primary-900/20 text-primary-400 p-3 rounded-lg text-sm flex items-center gap-2 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Selected: {selectedLanguage.label}
                </div>
              )}
            </div>

            {/* Modal footer */}
            <div className="p-5 border-t border-dark-100/50 flex justify-end gap-3 bg-dark-300/30 backdrop-blur-md">
              <button
                onClick={() => {
                  setIsCreateModelShow(false);
                  setName("");
                  setSelectedLanguage(null);
                }}
                className="px-4 py-2 bg-gray-700/70 hover:bg-gray-600/90 text-white rounded-lg transition-all duration-200 backdrop-blur-md border border-gray-500/20 hover:border-gray-500/40 shadow-sm hover:shadow-gray-500/20"
              >
                Cancel
              </button>
              <button
                onClick={createProj}
                disabled={!name || !selectedLanguage}
                className={`px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 backdrop-blur-md border shadow-sm ${name && selectedLanguage ? 'bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white border-primary-500/30 hover:border-primary-400/60 hover:shadow-primary-500/30' : 'bg-gray-800/70 text-gray-500 cursor-not-allowed border-gray-700/30'}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                Create Project
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Project Modal */}
      {isEditModelShow && (
        <div
          onClick={(e) => {
            if (e.target.classList.contains("modal-overlay")) {
              setIsEditModelShow(false);
              setName("");
              setEditProjId("");
            }
          }}
          className="modal-overlay fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        >
          <div
            className="bg-white/10 backdrop-blur-xl rounded-2xl overflow-hidden shadow-2xl border border-white/20 w-full max-w-md transition-all duration-300 transform"
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              boxShadow: '0 25px 45px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal header */}
            <div className="p-5 border-b border-primary-500/20 flex items-center justify-between bg-gradient-to-r from-primary-900/20 to-accent-900/20 backdrop-blur-md">
              <h3 className="text-xl font-semibold text-white">Edit Project</h3>
              <button
                onClick={() => {
                  setIsEditModelShow(false);
                  setName("");
                  setEditProjId("");
                }}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

            {/* Modal body */}
            <div className="p-5">
              <div className="mb-4">
                <label htmlFor="edit-project-name" className="block text-sm font-medium text-gray-400 mb-2">Project Name</label>
                <div className="inputBox">
                  <input
                    id="edit-project-name"
                    onChange={(e) => { setName(e.target.value) }}
                    value={name}
                    type="text"
                    placeholder="Enter project name"
                    className="text-white w-full"
                    autoFocus
                  />
                </div>
              </div>
            </div>

            {/* Modal footer */}
            <div className="p-5 border-t border-dark-100/50 flex justify-end gap-3 bg-dark-300/30 backdrop-blur-md">
              <button
                onClick={() => {
                  setIsEditModelShow(false);
                  setName("");
                  setEditProjId("");
                }}
                className="px-4 py-2 bg-gray-700/70 hover:bg-gray-600/90 text-white rounded-lg transition-all duration-200 backdrop-blur-md border border-gray-500/20 hover:border-gray-500/40 shadow-sm hover:shadow-gray-500/20"
              >
                Cancel
              </button>
              <button
                onClick={updateProj}
                disabled={!name}
                className={`px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 backdrop-blur-md border shadow-sm ${name ? 'bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white border-primary-500/30 hover:border-primary-400/60 hover:shadow-primary-500/30' : 'bg-gray-800/70 text-gray-500 cursor-not-allowed border-gray-700/30'}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                Update Project
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default Home;
