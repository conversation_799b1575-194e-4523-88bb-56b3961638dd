@tailwind base;
@tailwind components;
@tailwind utilities;

/* Restore default cursor throughout the website */
html, body, #root, #root * {
  cursor: auto;
}

/* Default cursor styles for interactive elements */
input, textarea, select, [contenteditable="true"] {
  cursor: text;
}

button, [role="button"], a, .link {
  cursor: pointer;
}

/* Animation keyframes */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.4;
  }
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink {
  50% { border-color: transparent }
}

/* Disable cursor animations on mobile devices */
@media (max-width: 768px) {
  .cursor-dot, .cursor-ring {
    display: none;
  }
}

/* Improve scrollbar appearance */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #d97706, #dc2626);
}

/* Add smooth scrolling to the whole page */
html {
  scroll-behavior: smooth;
}