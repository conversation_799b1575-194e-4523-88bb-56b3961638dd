import React, { useEffect, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import bgImage from '../images/bgimg/hassan-maayiz-PclsTXKOgHU-unsplash.jpg';

const BackgroundCodeLines = () => {
  const [lines, setLines] = useState([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);

  // Enhanced code snippets with space/sci-fi theme
  const codeSnippets = [
    'function initializeStarship() {',
    '  activateWarpDrive();',
    '  calibrateNavigation(coordinates);',
    '}',
    'class SpaceStation extends OrbitalBody {',
    '  constructor(position, velocity) {',
    '    super(position, velocity);',
    '    this.dockingPorts = [];',
    '  }',
    '}',
    'const galaxy = new GalacticMap();',
    'galaxy.scan(currentSector);',
    'galaxy.plotCourse(destination);',
    'const [shields, setShields] = useState(100);',
    'useEffect(() => {',
    '  // Monitor quantum fluctuations',
    '  return () => releaseParticles();',
    '}, [warpField]);',
    'if (asteroidField.detected) {',
    '  navigateAround(asteroidField);',
    '} else {',
    '  maintainCourse();',
    '}',
    'for (let i = 0; i < stars.length; i++) {',
    '  calculateDistance(ship.position, stars[i]);',
    '}',
    'planets.map(planet => analyzeAtmosphere(planet));',
    'const signal = await scanDeepSpace(coordinates);',
    'const data = decryptAlienTransmission(signal);',
    'function calculateTrajectory(mass, velocity) {',
    '  return mass * velocity * GRAVITATIONAL_CONSTANT;',
    '}',
    'class QuantumComputer {',
    '  solveEquation(problem) {',
    '    return this.qubits.process(problem);',
    '  }',
    '}'
  ];

  // Set up dimensions
  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Track mouse movement
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Generate lines
  useEffect(() => {
    if (dimensions.width === 0) return;

    const generateLines = () => {
      const newLines = [];
      const lineCount = Math.floor(dimensions.height / 30); // More lines (every 30px)

      for (let i = 0; i < lineCount; i++) {
        // Determine color based on random value
        const colorValue = Math.random();
        let color, glow;

        if (colorValue > 0.85) { // 15% chance for blue
          color = '#61dafb';
          glow = '0 0 10px rgba(97, 218, 251, 0.8), 0 0 20px rgba(97, 218, 251, 0.4)';
        } else if (colorValue > 0.7) { // 15% chance for purple
          color = '#a78bfa';
          glow = '0 0 10px rgba(167, 139, 250, 0.8), 0 0 20px rgba(167, 139, 250, 0.4)';
        } else if (colorValue > 0.55) { // 15% chance for cyan
          color = '#22d3ee';
          glow = '0 0 10px rgba(34, 211, 238, 0.8), 0 0 20px rgba(34, 211, 238, 0.4)';
        } else { // 55% chance for default color
          color = '#94a3b8';
          glow = '0 0 5px rgba(148, 163, 184, 0.5)';
        }

        newLines.push({
          id: i,
          text: codeSnippets[Math.floor(Math.random() * codeSnippets.length)],
          x: Math.random() * dimensions.width,
          y: i * 30 + Math.random() * 15, // Add some randomness to y position
          opacity: Math.random() * 0.5 + 0.2, // Higher opacity (0.2-0.7)
          speed: Math.random() * 1.2 + 0.3, // Faster speed (0.3-1.5)
          direction: Math.random() > 0.5 ? 1 : -1, // Random direction
          color: color,
          glow: glow,
          glowIntensity: Math.random() * 0.5 + 0.5 // Random glow intensity (0.5-1)
        });
      }

      setLines(newLines);
    };

    generateLines();
  }, [dimensions]);

  return (
    <div
      ref={containerRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        zIndex: -1,
        pointerEvents: 'none',
        background: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {lines.map(line => {
        // Calculate distance from mouse
        const dx = mousePosition.x - line.x;
        const dy = mousePosition.y - line.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const maxDistance = 300; // Max distance for mouse influence

        // Calculate glow intensity based on mouse proximity
        const proximityFactor = distance < maxDistance
          ? 1 - (distance / maxDistance)
          : 0;
        const glowIntensity = line.glowIntensity + (proximityFactor * 0.5);

        return (
          <motion.div
            key={line.id}
            initial={{ x: line.direction > 0 ? -200 : dimensions.width + 200, y: line.y }}
            animate={{
              x: line.direction > 0 ? dimensions.width + 200 : -200,
              opacity: [line.opacity, line.opacity * 1.5, line.opacity] // Pulsing opacity
            }}
            transition={{
              x: {
                duration: (dimensions.width + 400) / (80 * line.speed), // Faster movement
                repeat: Infinity,
                ease: 'linear'
              },
              opacity: {
                duration: 2 + (line.glowIntensity * 2), // Random pulse duration
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }}
            style={{
              position: 'absolute',
              fontFamily: 'JetBrains Mono, monospace',
              fontSize: '14px',
              color: line.color,
              opacity: line.opacity * glowIntensity,
              whiteSpace: 'nowrap',
              textShadow: line.glow,
              fontWeight: proximityFactor > 0.5 ? 'bold' : 'normal', // Bold when close to mouse
              letterSpacing: '0.5px' // Slightly spaced letters for better readability
            }}
          >
            {line.text}
          </motion.div>
        );
      })}
    </div>
  );
};

export default BackgroundCodeLines;
