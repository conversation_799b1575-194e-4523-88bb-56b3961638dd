import React, { useState } from 'react';
import logo from "../images/logos/logo.png"
import bgImage from "../images/bgimg/pexels-pixabay-70577.jpg"
import { Link, useNavigate } from 'react-router-dom';
import { api_base_url } from '../helper';
import { toast } from 'react-toastify';

const SignUp = () => {
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [pwd, setPwd] = useState("");

  const navigate = useNavigate();

  const submitForm = (e) => {
    e.preventDefault();
    fetch(api_base_url + "/signUp",{
      mode: "cors",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        fullName: fullName,
        email: email,
        pwd: pwd
      })
    }).then(res => res.json()).then(data => {
      if(data.success){
        // Save user's name to localStorage before navigating
        localStorage.setItem('userName', fullName);
        toast.success('Account created successfully!');
        navigate("/login");
      }
      else{
        toast.error(data.msg);
      }
    })
  };

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);
    submitForm(e);

    // Set a timeout to handle cases where the API might be slow
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8"
      style={{
        background: `
          linear-gradient(135deg, rgba(20, 20, 20, 0.85) 0%, rgba(30, 30, 30, 0.8) 50%, rgba(40, 40, 40, 0.75) 100%),
          url(${bgImage})
        `,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="max-w-md w-full space-y-8">
        {/* Logo and header */}
        <div className="text-center">
          <div>
            <img className="mx-auto h-16 w-auto" src={logo} alt="KodeBase Logo" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-white">
            Create your account
          </h2>
          <p className="mt-2 text-sm text-primary-300">
            Or{' '}
            <Link to="/login" className="font-medium text-primary-400 hover:text-primary-300 underline decoration-primary-500/50 hover:decoration-primary-400">
              sign in to your existing account
            </Link>
          </p>
        </div>

        {/* SignUp form */}
        <form 
          onSubmit={handleSubmit} 
          className="mt-8 space-y-6 bg-white/10 backdrop-blur-xl p-8 rounded-2xl shadow-2xl border border-white/20 hover:border-primary-400/40 transition-all duration-300"
          style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            boxShadow: '0 25px 45px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
          }}
        >
          <div className="space-y-4 rounded-md">
            <div>
              <label
                htmlFor="full-name"
                className="block text-sm font-medium text-primary-300 mb-2"
              >
                Full Name
              </label>
              <div className="inputBox">
                <input
                  id="full-name"
                  name="fullName"
                  type="text"
                  autoComplete="name"
                  required
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Enter your full name"
                  className="text-white w-full"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="email-address"
                className="block text-sm font-medium text-primary-300 mb-2"
              >
                Email address
              </label>
              <div className="inputBox">
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="text-white w-full"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-primary-300 mb-2"
              >
                Password
              </label>
              <div className="inputBox">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={pwd}
                  onChange={(e) => setPwd(e.target.value)}
                  placeholder="Create a password"
                  className="text-white w-full"
                />
              </div>
              <p className="mt-1 text-xs text-primary-300/60">
                Password should be at least 8 characters long
              </p>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className={`group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white ${isLoading ? 'bg-gradient-to-r from-primary-700 to-secondary-700' : 'bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-lg`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating account...
                </>
              ) : 'Create account'}
            </button>
          </div>

          <div className="text-xs text-primary-300/60">
            By signing up, you agree to our{' '}
            <Link to="#" className="font-medium text-primary-400 hover:text-primary-300">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link to="#" className="font-medium text-primary-400 hover:text-primary-300">
              Privacy Policy
            </Link>
          </div>
        </form>

        {/* Footer */}
        <div className="text-center mt-4">
          <p className="text-xs text-primary-300/60">
            &copy; {new Date().getFullYear()} KodeBase. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  )
}

export default SignUp
