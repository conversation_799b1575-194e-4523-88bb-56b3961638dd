import React, { useState } from 'react';
import logo from "../images/logos/logo.png"
import bgImage from "../images/bgimg/hassan-maayiz-PclsTXKOgHU-unsplash.jpg"
import { Link, useNavigate } from 'react-router-dom';
import { api_base_url } from '../helper';
import { toast } from 'react-toastify';

const SignUp = () => {
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [pwd, setPwd] = useState("");

  const navigate = useNavigate();

  const submitForm = (e) => {
    e.preventDefault();
    fetch(api_base_url + "/signUp",{
      mode: "cors",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        fullName: fullName,
        email: email,
        pwd: pwd
      })
    }).then(res => res.json()).then(data => {
      if(data.success){
        // Save user's name to localStorage before navigating
        localStorage.setItem('userName', fullName);
        toast.success('Account created successfully!');
        navigate("/login");
      }
      else{
        toast.error(data.msg);
      }
    })
  };

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);
    submitForm(e);

    // Set a timeout to handle cases where the API might be slow
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8"
      style={{
        background: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="max-w-md w-full space-y-8">
        {/* Logo and header */}
        <div className="text-center">
          <div>
            <img className="mx-auto h-16 w-auto" src={logo} alt="KodeBase Logo" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold bg-gradient-to-r from-orange-400 via-yellow-300 to-white text-transparent bg-clip-text">
            Create your account
          </h2>
          <p className="mt-2 text-sm bg-gradient-to-r from-white via-yellow-200 to-orange-300 text-transparent bg-clip-text">
            Or{' '}
            <Link to="/login" className="font-medium bg-gradient-to-r from-orange-400 to-yellow-400 text-transparent bg-clip-text hover:from-yellow-400 hover:to-orange-400 underline decoration-orange-500/50 hover:decoration-yellow-400">
              sign in to your existing account
            </Link>
          </p>
        </div>

        {/* SignUp form */}
        <form 
          onSubmit={handleSubmit} 
          className="mt-8 space-y-6 p-8 rounded-2xl glass-form hover:border-primary-400/40 transition-all duration-300"
        >
          <div className="space-y-4 rounded-md">
            <div>
              <label
                htmlFor="full-name"
                className="block text-sm font-medium bg-gradient-to-r from-orange-400 to-yellow-300 text-transparent bg-clip-text mb-2"
              >
                Full Name
              </label>
              <div className="inputBox">
                <input
                  id="full-name"
                  name="fullName"
                  type="text"
                  autoComplete="name"
                  required
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Enter your full name"
                  className="bg-gradient-to-r from-orange-400 via-yellow-300 to-white text-transparent bg-clip-text w-full"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="email-address"
                className="block text-sm font-medium bg-gradient-to-r from-orange-400 to-yellow-300 text-transparent bg-clip-text mb-2"
              >
                Email address
              </label>
              <div className="inputBox">
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="bg-gradient-to-r from-orange-400 via-yellow-300 to-white text-transparent bg-clip-text w-full"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium bg-gradient-to-r from-orange-400 to-yellow-300 text-transparent bg-clip-text mb-2"
              >
                Password
              </label>
              <div className="inputBox">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={pwd}
                  onChange={(e) => setPwd(e.target.value)}
                  placeholder="Create a password"
                  className="bg-gradient-to-r from-orange-400 via-yellow-300 to-white text-transparent bg-clip-text w-full"
                />
              </div>
              <p className="mt-1 text-xs bg-gradient-to-r from-white/60 via-yellow-200/60 to-orange-300/60 text-transparent bg-clip-text">
                Password should be at least 8 characters long
              </p>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className={`group relative w-full flex justify-center py-3 px-4 text-sm font-medium rounded-xl bg-gradient-to-r from-orange-400 via-yellow-300 to-white text-transparent bg-clip-text glass-button ${isLoading ? 'bg-gradient-to-r from-orange-700/80 to-yellow-700/80' : 'bg-gradient-to-r from-orange-600/80 to-yellow-600/80 hover:from-orange-700/90 hover:to-yellow-700/90'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500/50`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating account...
                </>
              ) : 'Create account'}
            </button>
          </div>

          <div className="text-xs bg-gradient-to-r from-white/60 via-yellow-200/60 to-orange-300/60 text-transparent bg-clip-text">
            By signing up, you agree to our{' '}
            <Link to="#" className="font-medium bg-gradient-to-r from-orange-400 to-yellow-400 text-transparent bg-clip-text hover:from-yellow-400 hover:to-orange-400">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link to="#" className="font-medium bg-gradient-to-r from-orange-400 to-yellow-400 text-transparent bg-clip-text hover:from-yellow-400 hover:to-orange-400">
              Privacy Policy
            </Link>
          </div>
        </form>

        {/* Footer */}
        <div className="text-center mt-4">
          <p className="text-xs bg-gradient-to-r from-white/60 via-yellow-200/60 to-orange-300/60 text-transparent bg-clip-text">
            &copy; {new Date().getFullYear()} KodeBase. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  )
}

export default SignUp
